# Permission Setup Commands for Lobby System
# Run these commands to set up proper permissions for the multi-world server

# Create permission groups
/lp creategroup default
/lp creategroup player
/lp creategroup moderator
/lp creategroup admin

# Set group weights (higher = more priority)
/lp group default setweight 1
/lp group player setweight 10
/lp group moderator setweight 50
/lp group admin setweight 100

# Default group permissions (for new players)
/lp group default permission set lobby.join true
/lp group default permission set lobby.compass true
/lp group default permission set multiverse.access.lobby true
/lp group default permission set essentials.spawn true
/lp group default permission set essentials.kit.starter true

# Player group permissions (registered players)
/lp group player parent add default
/lp group player permission set multiverse.access.world true
/lp group player permission set multiverse.teleport.world true
/lp group player permission set lobby.survival true
/lp group player permission set lobby.skywars true
/lp group player permission set skywars.join true
/lp group player permission set skywars.leave true
/lp group player permission set essentials.back true
/lp group player permission set essentials.home true
/lp group player permission set essentials.sethome true
/lp group player permission set essentials.tpa true
/lp group player permission set essentials.tpaccept true
/lp group player permission set essentials.tpdeny true

# Moderator permissions
/lp group moderator parent add player
/lp group moderator permission set multiverse.access.* true
/lp group moderator permission set multiverse.teleport.* true
/lp group moderator permission set skywars.admin.join true
/lp group moderator permission set skywars.admin.leave true
/lp group moderator permission set skywars.admin.start true
/lp group moderator permission set skywars.admin.stop true
/lp group moderator permission set essentials.kick true
/lp group moderator permission set essentials.mute true
/lp group moderator permission set essentials.tempban true
/lp group moderator permission set essentials.tp true
/lp group moderator permission set essentials.tphere true

# Admin permissions
/lp group admin permission set * true
/lp group admin permission set multiverse.* true
/lp group admin permission set skywars.* true
/lp group admin permission set essentials.* true
/lp group admin permission set worldedit.* true

# Set default group for new players
/lp group default permission set luckperms.user.parent.add.player true

# World-specific permissions
# Lobby world permissions
/lp group default permission set multiverse.access.lobby true
/lp group default permission set multiverse.exempt.lobby true

# Survival world permissions  
/lp group player permission set multiverse.access.world true
/lp group player permission set multiverse.teleport.world true

# SkyWars world permissions
/lp group player permission set multiverse.access.skywars_arena1 false
/lp group player permission set skywars.join true

# Sign permissions for lobby
/lp group default permission set essentials.signs.use.survival true
/lp group default permission set essentials.signs.use.skywars true
/lp group default permission set essentials.signs.use.lobby true
/lp group default permission set essentials.signs.use.info true
/lp group default permission set essentials.signs.use.rules true

# Create sign permissions
/lp group moderator permission set essentials.signs.create.survival true
/lp group moderator permission set essentials.signs.create.skywars true
/lp group moderator permission set essentials.signs.create.lobby true
/lp group admin permission set essentials.signs.create.* true

# Kit permissions
/lp group default permission set essentials.kit.starter true
/lp group player permission set essentials.kit.tools true
/lp group moderator permission set essentials.kit.* true

# Command permissions
/lp group default permission set essentials.help true
/lp group default permission set essentials.list true
/lp group default permission set essentials.msg true
/lp group default permission set essentials.r true

# Prevent certain commands in lobby
/lp group default permission set essentials.gamemode false
/lp group default permission set essentials.time false
/lp group default permission set essentials.weather false
/lp group default permission set worldedit.* false

# Allow moderators to use these commands
/lp group moderator permission set essentials.gamemode true
/lp group moderator permission set essentials.time true
/lp group moderator permission set essentials.weather true

# Auto-promote players after registration
# This would typically be handled by AuthMe hooks or a separate plugin
