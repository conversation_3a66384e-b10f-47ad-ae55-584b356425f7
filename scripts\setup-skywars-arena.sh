#!/bin/bash

# SkyWars Arena Setup Script
# This script creates a basic SkyWars arena structure

echo "Setting up SkyWars arena..."

# Wait for server to be ready
sleep 10

# Teleport to SkyWars world
rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv tp @p skywars_arena1"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "gamemode creative @p"

# Create center island (main island)
echo "Creating center island..."
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -5 60 -5 5 65 5 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -3 66 -3 3 66 3 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -2 67 -2 2 67 2 air"

# Place center chests
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 67 0 chest[facing=north]"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 2 67 0 chest[facing=west]"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -2 67 0 chest[facing=east]"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 67 2 chest[facing=south]"

# Create player spawn islands (8 islands around center)
echo "Creating player spawn islands..."

# Island 1 (North)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -1 60 -25 1 62 -23 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 63 -24 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 64 -24 chest[facing=south]"

# Island 2 (Northeast)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill 17 60 -18 19 62 -16 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 18 63 -17 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 18 64 -17 chest[facing=south]"

# Island 3 (East)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill 23 60 -1 25 62 1 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 24 63 0 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 24 64 0 chest[facing=west]"

# Island 4 (Southeast)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill 17 60 16 19 62 18 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 18 63 17 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 18 64 17 chest[facing=north]"

# Island 5 (South)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -1 60 23 1 62 25 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 63 24 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock 0 64 24 chest[facing=north]"

# Island 6 (Southwest)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -19 60 16 -17 62 18 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -18 63 17 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -18 64 17 chest[facing=north]"

# Island 7 (West)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -25 60 -1 -23 62 1 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -24 63 0 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -24 64 0 chest[facing=east]"

# Island 8 (Northwest)
rcon-cli -H localhost -p 25575 -P minecraft_rcon "fill -19 60 -18 -17 62 -16 stone"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -18 63 -17 grass_block"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "setblock -18 64 -17 chest[facing=south]"

# Create world border
echo "Setting world border..."
rcon-cli -H localhost -p 25575 -P minecraft_rcon "worldborder center 0 0"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "worldborder set 100"

# Set spawn points for SkyWars plugin
echo "Setting up SkyWars arena configuration..."
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw create arena1"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 1 0 64 -24"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 2 18 64 -17"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 3 24 64 0"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 4 18 64 17"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 5 0 64 24"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 6 -18 64 17"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 7 -24 64 0"
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw setspawn arena1 8 -18 64 -17"

# Enable the arena
rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw enable arena1"

echo "SkyWars arena setup complete!"
echo "Arena 'arena1' has been created with 8 spawn points"
echo "Players can now join using /sw join arena1"
