#!/bin/bash

echo "🔄 Updating Minecraft Server with Lobby System..."

# Stop containers gracefully
echo "📥 Stopping containers..."
docker-compose down

# Start containers with new configuration
echo "🚀 Starting containers with new configuration..."
docker-compose up -d

# Wait for containers to be ready
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo "📊 Container Status:"
docker-compose ps

# Monitor server startup
echo "📋 Monitoring server startup (press Ctrl+C to stop monitoring)..."
echo "Waiting for server to be ready..."

# Wait for server to be ready
while ! docker exec docker-minecraft-server-mc-1 rcon-cli -H localhost -p 25575 -P minecraft_rcon "list" 2>/dev/null; do
    echo "⏳ Server still starting up..."
    sleep 5
done

echo "✅ Server is ready!"
echo ""
echo "🎯 Next steps:"
echo "1. Run world setup: docker exec -it docker-minecraft-server-mc-1 bash /scripts/setup-worlds.sh"
echo "2. Run arena setup: docker exec -it docker-minecraft-server-mc-1 bash /scripts/setup-skywars-arena.sh"
echo "3. Configure permissions using scripts/setup-permissions.txt"
echo "4. Build lobby using scripts/lobby-commands.txt"
echo ""
echo "📖 See LOBBY_SETUP_GUIDE.md for detailed instructions"
