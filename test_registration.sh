#!/bin/bash

# Test script to verify registration limit is working
echo "Testing registration limit functionality..."

# Add test users directly to database to simulate multiple registrations from same IP
TEST_IP="**********"
PASSWORD_HASH='$2a$10$example.hash.here.for.password123'

echo "Current users from IP $TEST_IP:"
./database/run_sql.sh "SELECT username, regip FROM authme WHERE regip = '$TEST_IP';"

echo ""
echo "Adding test users to simulate multiple registrations from same IP..."

# Add test users 2-5
for i in {2..5}; do
    echo "Adding testuser$i..."
    ./database/run_sql.sh "INSERT IGNORE INTO authme (username, realname, password, regdate, regip, world) VALUES ('testuser$i', 'TestUser$i', '$PASSWORD_HASH', UNIX_TIMESTAMP(), '$TEST_IP', 'world');"
done

echo ""
echo "Current user count from IP $TEST_IP:"
./database/run_sql.sh "SELECT COUNT(*) as user_count FROM authme WHERE regip = '$TEST_IP';"

echo ""
echo "All users from IP $TEST_IP:"
./database/run_sql.sh "SELECT username, regip FROM authme WHERE regip = '$TEST_IP';"

echo ""
echo "Test completed. With maxRegPerIp set to 50, this IP should be able to register many more users."
echo "The previous limit was 1, which would have prevented additional registrations."
