#!/bin/bash

echo "🏟️ Setting up Basic SkyWars Arena..."

# Function to run RC<PERSON> commands
run_rcon() {
    docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "$1"
}

echo "🌍 Teleporting to SkyWars world and building arena..."

# Clear the area and create a basic SkyWars arena
echo "Creating center island..."
run_rcon "execute in minecraft:overworld run fill -5 60 -5 5 65 5 stone"
run_rcon "execute in minecraft:overworld run fill -3 66 -3 3 66 3 grass_block"
run_rcon "execute in minecraft:overworld run fill -2 67 -2 2 67 2 air"

# Place center chests
echo "Placing center chests..."
run_rcon "execute in minecraft:overworld run setblock 0 67 0 chest"
run_rcon "execute in minecraft:overworld run setblock 2 67 0 chest"
run_rcon "execute in minecraft:overworld run setblock -2 67 0 chest"
run_rcon "execute in minecraft:overworld run setblock 0 67 2 chest"

# Create player spawn islands (8 islands around center)
echo "Creating player spawn islands..."

# Island 1 (North)
run_rcon "execute in minecraft:overworld run fill -1 60 -25 1 62 -23 stone"
run_rcon "execute in minecraft:overworld run setblock 0 63 -24 grass_block"
run_rcon "execute in minecraft:overworld run setblock 0 64 -24 chest"

# Island 2 (Northeast)  
run_rcon "execute in minecraft:overworld run fill 17 60 -18 19 62 -16 stone"
run_rcon "execute in minecraft:overworld run setblock 18 63 -17 grass_block"
run_rcon "execute in minecraft:overworld run setblock 18 64 -17 chest"

# Island 3 (East)
run_rcon "execute in minecraft:overworld run fill 23 60 -1 25 62 1 stone"
run_rcon "execute in minecraft:overworld run setblock 24 63 0 grass_block"
run_rcon "execute in minecraft:overworld run setblock 24 64 0 chest"

# Island 4 (Southeast)
run_rcon "execute in minecraft:overworld run fill 17 60 16 19 62 18 stone"
run_rcon "execute in minecraft:overworld run setblock 18 63 17 grass_block"
run_rcon "execute in minecraft:overworld run setblock 18 64 17 chest"

# Island 5 (South)
run_rcon "execute in minecraft:overworld run fill -1 60 23 1 62 25 stone"
run_rcon "execute in minecraft:overworld run setblock 0 63 24 grass_block"
run_rcon "execute in minecraft:overworld run setblock 0 64 24 chest"

# Island 6 (Southwest)
run_rcon "execute in minecraft:overworld run fill -19 60 16 -17 62 18 stone"
run_rcon "execute in minecraft:overworld run setblock -18 63 17 grass_block"
run_rcon "execute in minecraft:overworld run setblock -18 64 17 chest"

# Island 7 (West)
run_rcon "execute in minecraft:overworld run fill -25 60 -1 -23 62 1 stone"
run_rcon "execute in minecraft:overworld run setblock -24 63 0 grass_block"
run_rcon "execute in minecraft:overworld run setblock -24 64 0 chest"

# Island 8 (Northwest)
run_rcon "execute in minecraft:overworld run fill -19 60 -18 -17 62 -16 stone"
run_rcon "execute in minecraft:overworld run setblock -18 63 -17 grass_block"
run_rcon "execute in minecraft:overworld run setblock -18 64 -17 chest"

# Set world border
echo "Setting world border..."
run_rcon "worldborder center 0 0"
run_rcon "worldborder set 100"

# Configure world settings for SkyWars
echo "Configuring SkyWars world settings..."
run_rcon "gamerule keepInventory false"
run_rcon "gamerule naturalRegeneration true"
run_rcon "gamerule doDaylightCycle false"
run_rcon "time set day"

echo "✅ Basic SkyWars arena created!"
echo ""
echo "🎮 How to join SkyWars:"
echo "  1. Use: /mv tp <player> skywars1"
echo "  2. Or click the SkyWars sign in lobby"
echo "  3. Players will be teleported to the arena"
echo ""
echo "⚔️ Arena Features:"
echo "  ✅ Center island with 4 chests"
echo "  ✅ 8 spawn islands around the center"
echo "  ✅ Each island has a chest with loot"
echo "  ✅ 100 block world border"
echo "  ✅ PvP enabled"
echo ""
echo "🔧 Admin Commands:"
echo "  /mv tp <player> skywars1    - Send player to SkyWars"
echo "  /mv tp <player> lobby       - Return player to lobby"
echo "  /gamemode survival <player> - Set survival mode"
echo "  /clear <player>             - Clear player inventory"
